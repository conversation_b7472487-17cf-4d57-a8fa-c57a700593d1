<?php
/**
 * Application Initialization
 * Sets up the environment, includes classes, and starts session
 */

// Prevent direct access
if (!defined('CMS_INIT')) {
    die('Direct access not allowed');
}

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set timezone
date_default_timezone_set('UTC');

// Define paths
define('ROOT_PATH', dirname(dirname(__DIR__)));
define('SRC_PATH', ROOT_PATH . '/src');
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('DATABASE_PATH', ROOT_PATH . '/database');
define('LOGS_PATH', ROOT_PATH . '/logs');

// Include classes
require_once SRC_PATH . '/classes/Database.php';
require_once SRC_PATH . '/classes/Security.php';
require_once SRC_PATH . '/classes/Session.php';
require_once SRC_PATH . '/classes/Auth.php';
require_once SRC_PATH . '/classes/Post.php';

// Include utility functions
require_once SRC_PATH . '/includes/functions.php';

// Set security headers
Security::setSecurityHeaders();

// Start session
Session::start();

// Clean expired sessions periodically (1% chance)
if (rand(1, 100) === 1) {
    Session::cleanExpiredSessions();
}

// Check if CMS is installed
try {
    $db = Database::getInstance();
    if (!$db->isInstalled()) {
        // Redirect to installation if not installed
        if (basename($_SERVER['PHP_SELF']) !== 'install.php') {
            header('Location: install.php');
            exit;
        }
    }
} catch (Exception $e) {
    // Database connection failed, likely not installed
    if (basename($_SERVER['PHP_SELF']) !== 'install.php') {
        header('Location: install.php');
        exit;
    }
}

// Global error handler
function handleError($errno, $errstr, $errfile, $errline) {
    $logMessage = date('Y-m-d H:i:s') . " - Error [$errno]: $errstr in $errfile on line $errline\n";
    error_log($logMessage, 3, LOGS_PATH . '/error.log');
    
    // Don't show errors to users in production
    if (ini_get('display_errors')) {
        echo "An error occurred. Please check the logs.";
    }
    
    return true;
}

// Global exception handler
function handleException($exception) {
    $logMessage = date('Y-m-d H:i:s') . " - Uncaught exception: " . $exception->getMessage() . 
                  " in " . $exception->getFile() . " on line " . $exception->getLine() . "\n";
    error_log($logMessage, 3, LOGS_PATH . '/error.log');
    
    // Don't show exception details to users in production
    if (ini_get('display_errors')) {
        echo "An unexpected error occurred. Please check the logs.";
    }
}

// Set error and exception handlers
set_error_handler('handleError');
set_exception_handler('handleException');

// Initialize global objects
$auth = new Auth();
$postManager = new Post();

// Helper function to check if we're in admin area
function isAdminArea() {
    return strpos($_SERVER['REQUEST_URI'], '/admin/') !== false;
}

// Helper function to get base URL
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $path = dirname($_SERVER['SCRIPT_NAME']);
    return $protocol . '://' . $host . $path;
}

// Helper function to redirect
function redirect($url, $permanent = false) {
    $statusCode = $permanent ? 301 : 302;
    http_response_code($statusCode);
    header('Location: ' . $url);
    exit;
}

// Helper function to generate pagination links
function generatePagination($currentPage, $totalPages, $baseUrl) {
    if ($totalPages <= 1) {
        return '';
    }
    
    $html = '<div class="pagination">';
    
    // Previous page
    if ($currentPage > 1) {
        $prevPage = $currentPage - 1;
        $html .= '<a href="' . $baseUrl . '?page=' . $prevPage . '" class="page-link">&laquo; Previous</a>';
    }
    
    // Page numbers
    $start = max(1, $currentPage - 2);
    $end = min($totalPages, $currentPage + 2);
    
    if ($start > 1) {
        $html .= '<a href="' . $baseUrl . '?page=1" class="page-link">1</a>';
        if ($start > 2) {
            $html .= '<span class="page-ellipsis">...</span>';
        }
    }
    
    for ($i = $start; $i <= $end; $i++) {
        if ($i == $currentPage) {
            $html .= '<span class="page-link current">' . $i . '</span>';
        } else {
            $html .= '<a href="' . $baseUrl . '?page=' . $i . '" class="page-link">' . $i . '</a>';
        }
    }
    
    if ($end < $totalPages) {
        if ($end < $totalPages - 1) {
            $html .= '<span class="page-ellipsis">...</span>';
        }
        $html .= '<a href="' . $baseUrl . '?page=' . $totalPages . '" class="page-link">' . $totalPages . '</a>';
    }
    
    // Next page
    if ($currentPage < $totalPages) {
        $nextPage = $currentPage + 1;
        $html .= '<a href="' . $baseUrl . '?page=' . $nextPage . '" class="page-link">Next &raquo;</a>';
    }
    
    $html .= '</div>';
    
    return $html;
}
?>
