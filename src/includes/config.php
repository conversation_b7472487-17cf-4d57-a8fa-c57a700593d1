<?php
/**
 * Server-Agnostic Configuration
 * Settings that replace .htaccess functionality
 */

// Prevent direct access
if (!defined('CMS_INIT')) {
    die('Direct access not allowed');
}

class Config {
    
    /**
     * Security settings
     */
    const FORCE_HTTPS = false; // Set to true to force HTTPS redirects
    const ENABLE_COMPRESSION = true;
    const ENABLE_CACHING = true;
    
    /**
     * File access settings
     */
    const BLOCKED_EXTENSIONS = [
        'htaccess', 'htpasswd', 'ini', 'log', 'sh', 'sql', 'conf',
        'db', 'bak', 'backup', 'old', 'orig', 'save', 'swp', 'tmp'
    ];
    
    const BLOCKED_DIRECTORIES = ['/src/', '/database/', '/logs/'];
    
    const EXECUTABLE_EXTENSIONS = [
        'php', 'phtml', 'php3', 'php4', 'php5', 'pl', 'py', 
        'jsp', 'asp', 'sh', 'cgi', 'exe', 'bat', 'cmd'
    ];
    
    /**
     * Cache settings (in seconds)
     */
    const CACHE_TIMES = [
        'css' => 2592000,      // 30 days
        'js' => 2592000,       // 30 days
        'images' => 2592000,   // 30 days
        'fonts' => 2592000,    // 30 days
        'html' => 3600,        // 1 hour
        'api' => 0             // No cache
    ];
    
    /**
     * MIME types for file serving
     */
    const MIME_TYPES = [
        'txt' => 'text/plain',
        'html' => 'text/html',
        'htm' => 'text/html',
        'css' => 'text/css',
        'js' => 'application/javascript',
        'json' => 'application/json',
        'xml' => 'application/xml',
        'pdf' => 'application/pdf',
        'zip' => 'application/zip',
        'png' => 'image/png',
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'gif' => 'image/gif',
        'svg' => 'image/svg+xml',
        'ico' => 'image/x-icon',
        'webp' => 'image/webp',
        'woff' => 'font/woff',
        'woff2' => 'font/woff2',
        'ttf' => 'font/ttf',
        'eot' => 'application/vnd.ms-fontobject',
        'mp3' => 'audio/mpeg',
        'mp4' => 'video/mp4',
        'avi' => 'video/x-msvideo',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'xls' => 'application/vnd.ms-excel',
        'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];
    
    /**
     * Compressible content types
     */
    const COMPRESSIBLE_TYPES = [
        'text/html',
        'text/css',
        'text/plain',
        'text/xml',
        'application/javascript',
        'application/json',
        'application/xml',
        'application/rss+xml',
        'application/xhtml+xml',
        'image/svg+xml'
    ];
    
    /**
     * Valid post categories (for routing)
     */
    const VALID_CATEGORIES = ['tech', 'gaming', 'film', 'serie'];
    
    /**
     * Rate limiting settings
     */
    const RATE_LIMIT_ATTEMPTS = 5;
    const RATE_LIMIT_WINDOW = 900; // 15 minutes
    
    /**
     * Session settings
     */
    const SESSION_LIFETIME = 3600; // 1 hour
    const SESSION_CLEANUP_PROBABILITY = 1; // 1% chance
    
    /**
     * Get configuration value
     */
    public static function get($key, $default = null) {
        return defined("self::$key") ? constant("self::$key") : $default;
    }
    
    /**
     * Check if file extension is blocked
     */
    public static function isBlockedExtension($extension) {
        return in_array(strtolower($extension), self::BLOCKED_EXTENSIONS);
    }
    
    /**
     * Check if directory is blocked
     */
    public static function isBlockedDirectory($path) {
        foreach (self::BLOCKED_DIRECTORIES as $blockedDir) {
            if (strpos($path, $blockedDir) !== false) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Check if file is executable
     */
    public static function isExecutableExtension($extension) {
        return in_array(strtolower($extension), self::EXECUTABLE_EXTENSIONS);
    }
    
    /**
     * Get MIME type for extension
     */
    public static function getMimeType($extension) {
        return self::MIME_TYPES[strtolower($extension)] ?? 'application/octet-stream';
    }
    
    /**
     * Get cache time for file type
     */
    public static function getCacheTime($extension) {
        $ext = strtolower($extension);
        
        if (in_array($ext, ['css'])) return self::CACHE_TIMES['css'];
        if (in_array($ext, ['js'])) return self::CACHE_TIMES['js'];
        if (in_array($ext, ['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp', 'ico'])) return self::CACHE_TIMES['images'];
        if (in_array($ext, ['woff', 'woff2', 'ttf', 'eot'])) return self::CACHE_TIMES['fonts'];
        if (in_array($ext, ['html', 'htm'])) return self::CACHE_TIMES['html'];
        
        return 0; // No cache by default
    }
    
    /**
     * Check if content type is compressible
     */
    public static function isCompressible($contentType) {
        foreach (self::COMPRESSIBLE_TYPES as $type) {
            if (strpos($contentType, $type) !== false) {
                return true;
            }
        }
        return false;
    }
}
?>
