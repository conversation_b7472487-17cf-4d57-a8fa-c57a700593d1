<?php
/**
 * Session Management Class
 * Handles secure session operations and database session storage
 */

class Session {
    private static $started = false;
    
    /**
     * Start secure session
     */
    public static function start() {
        if (self::$started) {
            return;
        }
        
        // Configure session security
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 1 : 0);
        ini_set('session.cookie_samesite', 'Strict');
        ini_set('session.use_strict_mode', 1);
        ini_set('session.cookie_lifetime', 0); // Session cookie
        
        session_start();
        self::$started = true;
        
        // Regenerate session ID periodically for security
        if (!isset($_SESSION['last_regeneration'])) {
            self::regenerateId();
        } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
            self::regenerateId();
        }
    }
    
    /**
     * Regenerate session ID
     */
    public static function regenerateId() {
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }
    
    /**
     * Set session variable
     */
    public static function set($key, $value) {
        self::start();
        $_SESSION[$key] = $value;
    }
    
    /**
     * Get session variable
     */
    public static function get($key, $default = null) {
        self::start();
        return $_SESSION[$key] ?? $default;
    }
    
    /**
     * Check if session variable exists
     */
    public static function has($key) {
        self::start();
        return isset($_SESSION[$key]);
    }
    
    /**
     * Remove session variable
     */
    public static function remove($key) {
        self::start();
        unset($_SESSION[$key]);
    }
    
    /**
     * Destroy session completely
     */
    public static function destroy() {
        self::start();
        
        // Remove session from database if user is logged in
        if (self::has('user_id')) {
            $db = Database::getInstance();
            $db->query("DELETE FROM sessions WHERE id = ?", [session_id()]);
        }
        
        // Clear session data
        $_SESSION = [];
        
        // Delete session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        session_destroy();
        self::$started = false;
    }
    
    /**
     * Store session in database for logged-in users
     */
    public static function storeInDatabase($userId) {
        $db = Database::getInstance();
        $sessionId = session_id();
        $ip = Security::getClientIP();
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        // Remove old sessions for this user (optional: limit concurrent sessions)
        $db->query("DELETE FROM sessions WHERE user_id = ?", [$userId]);
        
        // Store new session
        $db->query(
            "INSERT INTO sessions (id, user_id, ip_address, user_agent) VALUES (?, ?, ?, ?)",
            [$sessionId, $userId, $ip, $userAgent]
        );
    }
    
    /**
     * Validate session from database
     */
    public static function validateFromDatabase() {
        if (!self::has('user_id')) {
            return false;
        }
        
        $db = Database::getInstance();
        $sessionId = session_id();
        $userId = self::get('user_id');
        
        $session = $db->fetchOne(
            "SELECT * FROM sessions WHERE id = ? AND user_id = ?",
            [$sessionId, $userId]
        );
        
        if (!$session) {
            // Session not found in database, destroy it
            self::destroy();
            return false;
        }
        
        // Update last activity
        $db->query(
            "UPDATE sessions SET last_activity = CURRENT_TIMESTAMP WHERE id = ?",
            [$sessionId]
        );
        
        return true;
    }
    
    /**
     * Clean expired sessions from database
     */
    public static function cleanExpiredSessions($maxAge = 86400) { // 24 hours
        $db = Database::getInstance();
        $db->query(
            "DELETE FROM sessions WHERE last_activity < datetime('now', '-{$maxAge} seconds')"
        );
    }
    
    /**
     * Check if user is logged in
     */
    public static function isLoggedIn() {
        return self::has('user_id') && self::validateFromDatabase();
    }
    
    /**
     * Get logged-in user ID
     */
    public static function getUserId() {
        return self::isLoggedIn() ? self::get('user_id') : null;
    }
    
    /**
     * Login user
     */
    public static function login($userId) {
        self::regenerateId();
        self::set('user_id', $userId);
        self::storeInDatabase($userId);
    }
    
    /**
     * Logout user
     */
    public static function logout() {
        self::destroy();
    }
}
?>
