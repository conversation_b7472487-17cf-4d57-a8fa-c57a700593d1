<?php
/**
 * Router for PHP Development Server
 * Handles URL rewriting for SEO-friendly URLs
 *
 * Note: This file is only needed when using PHP's built-in development server.
 * For production with Apache, the .htaccess file handles URL rewriting.
 */

$requestUri = $_SERVER['REQUEST_URI'];
$path = parse_url($requestUri, PHP_URL_PATH);

// Remove leading slash and split into parts
$pathParts = explode('/', trim($path, '/'));

// Handle SEO-friendly post URLs: /category/slug/
if (count($pathParts) >= 2) {
    $category = $pathParts[0];
    $slug = $pathParts[1];
    
    // Check if it's a valid category
    $validCategories = ['tech', 'gaming', 'film', 'serie'];
    if (in_array($category, $validCategories) && !empty($slug)) {
        // Set the path for post-seo.php to handle
        $_SERVER['REQUEST_URI'] = "/{$category}/{$slug}/";
        require_once 'post-seo.php';
        return;
    }
}

// Handle pagination URLs: /page/X/
if (count($pathParts) >= 2 && $pathParts[0] === 'page' && is_numeric($pathParts[1])) {
    $_GET['page'] = $pathParts[1];
    require_once 'index.php';
    return;
}

// Handle admin URLs
if (count($pathParts) >= 1 && $pathParts[0] === 'admin') {
    // Let the server handle admin files normally
    return false;
}

// Handle assets and other static files
if (preg_match('/\.(css|js|png|jpg|jpeg|gif|ico|svg)$/', $path)) {
    return false; // Let the server handle static files
}

// For all other requests, check if the file exists
if (file_exists(__DIR__ . $path)) {
    return false; // Let the server handle existing files
}

// Default to index.php for root and unknown paths
if ($path === '/' || empty($pathParts[0])) {
    require_once 'index.php';
    return;
}

// 404 for everything else
http_response_code(404);
require_once '404.php';
?>
